import { useState, useRef, useEffect, Suspense } from 'react';
import { useParams } from 'react-router-dom';
import { useNotification } from '../components/notification/notification';
import './RenderPage.css';
import { SearchBox } from '../components/search-box/search-box';
import { PrimaryButton } from '../components/primary-button/primary-button';
import { SecondaryButton } from '../components/secondary-button/secondary-button';
import { DropDown } from '../components/drop-down/drop-down';
import { TabGroup } from '../components/tab-group/tab-group';
import { TabItem } from '../components/tab-item/tab-item';
import { CustomMaterialPanel } from '../components/custom-material-panel/custom-material-panel';
import { Copy, Download, Upload, HelpCircle, RotateCcw, Box, Paintbrush } from 'lucide-react';
import LogoImage from 'src/assets/images/Logo.png';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Environment, useGLTF, Stage, Html } from '@react-three/drei';
import * as THREE from 'three';
import MaterialThumbnail from '../components/material-thumbnail/material-thumbnail';
import { UploadModelModal } from '../components/upload-model-modal/upload-model-modal';

import { apiService } from '../services/api';
import type { ModelData, MaterialData } from '../services/api';

interface MaterialProps {
  color: string;
  metalness: number; // 0-1
  roughness: number; // 0-1
  opacity: number;   // 0-1, 1 为不透明
}

// 每个 Mesh 名称 -> 对应材质
type PerMeshMaterials = Record<string, MaterialProps>;

// 原始材质信息
interface OriginalMaterial {
  name: string;
  meshName: string;
  color: string;
  metalness: number;
  roughness: number;
  opacity: number;
  isEditable: boolean; // 是否以Editable开头
}

interface ModelProps {
  modelPath: string;
  perMeshMaterials: PerMeshMaterials;
  activeMaterialName: string | null; // 当前激活的材质名称，用于高亮
  onOriginalMaterialsExtracted: (materials: OriginalMaterial[]) => void;
}

const Model = ({ modelPath, perMeshMaterials, activeMaterialName, onOriginalMaterialsExtracted }: ModelProps) => {
  const { scene } = useGLTF(modelPath);
  const [materialsExtracted, setMaterialsExtracted] = useState(false);

  // 当模型路径变化时重置材质提取状态
  useEffect(() => {
    setMaterialsExtracted(false);
  }, [modelPath]);

  useEffect(() => {
    // ---------- 统一模型归一化：重置、缩放、居中 ----------
    // ① 重置
    scene.position.set(0, 0, 0);
    scene.rotation.set(0, 0, 0);
    scene.scale.set(1, 1, 1);

    // ② 计算包围盒尺寸
    const box = new THREE.Box3().setFromObject(scene);
    const size = new THREE.Vector3();
    box.getSize(size);
    const maxDim = Math.max(size.x, size.y, size.z);

    // ③ 计算缩放系数，让最长边 = TARGET_SIZE
    const TARGET_SIZE = 2;  // 统一的目标长度
    const scale = TARGET_SIZE / maxDim;
    scene.scale.setScalar(scale);

    // ④ 重新计算包围盒并居中
    box.setFromObject(scene);
    const center = new THREE.Vector3();
    box.getCenter(center);
    scene.position.set(-center.x, -center.y, -center.z);

    // 只在首次加载时提取原始材质信息
    if (!materialsExtracted) {
      const originalMaterials: OriginalMaterial[] = [];
      scene.traverse((object) => {
        if (object instanceof THREE.Mesh && object.material) {
          const material = object.material as THREE.MeshStandardMaterial;
          const materialName = material.name || `Material_${object.name}`;

          originalMaterials.push({
            name: materialName,
            meshName: object.name,
            color: `#${material.color.getHexString()}`,
            metalness: material.metalness || 0,
            roughness: material.roughness || 0.5,
            opacity: material.opacity || 1,
            isEditable: materialName.startsWith('Editable')
          });
        }
      });

      // 通知父组件原始材质信息
      onOriginalMaterialsExtracted(originalMaterials);
      setMaterialsExtracted(true);
    }

    scene.traverse((object) => {
      if (object instanceof THREE.Mesh && object.material) {
        // 若用户为该 mesh 指定了材质，则覆盖原始材质
        if (perMeshMaterials[object.name]) {
          const mat = perMeshMaterials[object.name];
          const newMat = new THREE.MeshStandardMaterial({
            color: new THREE.Color(mat.color),
            metalness: mat.metalness,
            roughness: mat.roughness,
            transparent: mat.opacity < 1,
            opacity: mat.opacity,
          });
          object.material = newMat;
        }
        // 否则保留模型的原始材质


      }
    });
  }, [scene, perMeshMaterials, activeMaterialName]);

  return (
    <group>
      <primitive object={scene} />
    </group>
  )
}

const Loader = () => {
  return (
    <Html center>
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <div className="loading-text">加载中...</div>
      </div>
    </Html>
  );
};

interface RenderPageProps {
  theme?: 'light' | 'dark';
}

const RenderPage = ({ theme = 'dark' }: RenderPageProps) => {
  const controlsRef = useRef<any>(null);
  const { modelId } = useParams<{ modelId?: string }>();

  // 重置相机到默认视角
  const resetView = () => {
    if (controlsRef.current) {
      controlsRef.current.reset();
      controlsRef.current.target.set(0, 0, 0);
      controlsRef.current.object.position.set(0, 1, 4);
      controlsRef.current.update();
    }
  };
  // 初始为空，等后台数据返回后自动赋值
  const [selectedModel, setSelectedModel] = useState<string>(modelId || '');
  const [searchQuery, setSearchQuery] = useState('');
  // 当前选中的系统预设材质 ID
  const [selectedPresetId, setSelectedPresetId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('会通材料');
  
  // 存储模型列表和选中的模型数据
  const [models, setModels] = useState<ModelData[]>([]);
  const [currentModel, setCurrentModel] = useState<ModelData | null>(null);
  // 存储上传的模型URL
  const [uploadedModelUrl, setUploadedModelUrl] = useState<string | null>(null);
  
  // 存储材质列表和选中的材质数据
  const [materials, setMaterials] = useState<MaterialData[]>([]);

  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);
  
  // 显示加载状态和后台管理入口相关逻辑
  const showLoader = loading && (!currentModel || !currentModel.filePath);
  const [logoClickCount, setLogoClickCount] = useState(0);
  const logoClickTimer = useRef<number | null>(null);
  

  // 原始材质信息
  const [originalMaterials, setOriginalMaterials] = useState<OriginalMaterial[]>([]);
  // 当前激活的材质名称
  const [activeMaterialName, setActiveMaterialName] = useState<string | null>(null);
  // 每个 mesh 对应的材质设置
  const [perMeshMaterials, setPerMeshMaterials] = useState<PerMeshMaterials>({});
  
  // 处理模型文件上传
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const handleModelUpload = (file: File) => {
    // 检查文件类型
    const fileType = file.name.split('.').pop()?.toLowerCase() || '';
    if (!['glb', 'gltf'].includes(fileType)) {
      alert('请上传 GLB 或 GLTF 格式的模型文件');
      return;
    }
    const url = URL.createObjectURL(file);
    setUploadedModelUrl(url);
    const tempModel: ModelData = {
      id: 'uploaded-model',
      name: file.name,
      filePath: url,
      thumbnail: '',
      fileType,
      size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
      createdAt: new Date().toISOString()
    };
    setCurrentModel(tempModel);
    setActiveMaterialName(null);
    setOriginalMaterials([]);
    setPerMeshMaterials({});
    setUploadModalVisible(false);
  };

  // 处理原始材质信息提取
  const handleOriginalMaterialsExtracted = (materials: OriginalMaterial[]) => {
    setOriginalMaterials(materials);
    // 如果有材质，默认激活第一个
    if (materials.length > 0) {
      setActiveMaterialName(materials[0].name);
    }
  };

  // 处理材质激活
  const handleMaterialActivate = (materialName: string) => {
    setActiveMaterialName(materialName);
  };

  // 应用预设材质到激活的材质
  const applyPresetMaterialToActive = (material: MaterialData) => {
    if (!activeMaterialName) return;

    const activeMaterial = originalMaterials.find(m => m.name === activeMaterialName);
    if (!activeMaterial || !activeMaterial.isEditable) {
      // 只有可编辑材质才能应用预设材质
      return;
    }

    const newSettings = {
      color: material.color,
      metalness: material.metalness / 100,
      roughness: material.roughness / 100,
      opacity: material.glass ? (100 - material.glass) / 100 : 1
    };

    // 应用到对应的mesh
    setPerMeshMaterials(prev => ({
      ...prev,
      [activeMaterial.meshName]: newSettings
    }));
  };

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // 获取模型数据
        const modelData = await apiService.getModels();
        setModels(modelData);

        // 优先使用URL参数中的modelId，否则使用selectedModel，最后使用第一个模型
        const targetModelId = modelId || selectedModel;
        if (targetModelId && modelData.length > 0) {
          const found = modelData.find(m => m.id === targetModelId);
          if (found) {
            setSelectedModel(targetModelId);
            setCurrentModel(found);
          } else if (modelData.length > 0) {
            // 如果指定的模型不存在，使用第一个模型
            setSelectedModel(modelData[0].id);
            setCurrentModel(modelData[0]);
          }
        } else if (!targetModelId && modelData.length > 0) {
          // 如果没有指定模型，自动选中第一个
          setSelectedModel(modelData[0].id);
          setCurrentModel(modelData[0]);
        }

        // 获取材质数据
        const materialData = await apiService.getMaterials();
        setMaterials(materialData);
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchData();
    // 依赖modelId和selectedModel的变化
  }, [modelId, selectedModel]);
  
  // 当选择模型变化时更新当前模型数据
  useEffect(() => {
    console.log('models 数据:', models);
    console.log('当前选中的模型 ID:', selectedModel);
    
    if (!selectedModel || models.length === 0) {
      setCurrentModel(null);
      return;
    }
    const model = models.find(m => m.id === selectedModel);
    console.log('找到的模型:', model);
    setCurrentModel(model || null);
  }, [selectedModel, models]);
  

  const handleLogoClick = () => {
    const newCount = logoClickCount + 1;
    setLogoClickCount(newCount);
    
    // 清除之前的计时器
    if (logoClickTimer.current) {
      window.clearTimeout(logoClickTimer.current);
    }
    
    // 设置新的计时器，2秒内未达到3次点击则重置计数
    logoClickTimer.current = window.setTimeout(() => {
      setLogoClickCount(0);
    }, 2000);
    
    // 如果达到3次点击，跳转到后台登录页面
    if (newCount === 3) {
      setLogoClickCount(0);
      if (logoClickTimer.current) {
        window.clearTimeout(logoClickTimer.current);
      }
      window.location.href = '/admin';
    }
  };

  // -------------- 新增：Canvas 引用及复制 / 保存功能 --------------
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const notify = useNotification();

  const handleCopyImage = async () => {
    try {
      const canvas = canvasRef.current;
      if (!canvas) return;
      
      // 定义 Clipboard 接口的类型声明
      interface ExtendedClipboard extends Clipboard {
        write(data: ClipboardItem[]): Promise<void>;
      }
      
      interface ClipboardItemConstructor {
        new (items: Record<string, Blob>): ClipboardItem;
      }
      
      canvas.toBlob(async (blob) => {
        if (!blob) return;
        
        try {
          // 使用类型断言处理实验性 API
          const clipboard = navigator.clipboard as unknown as ExtendedClipboard;
          const ClipboardItem = window.ClipboardItem as unknown as ClipboardItemConstructor;
          
          if (!ClipboardItem) {
            throw new Error('Clipboard API 不支持 ClipboardItem');
          }
          
          const item = new ClipboardItem({ 'image/png': blob });
          await clipboard.write([item]);
          notify('图片已复制到剪贴板', 'success');
        } catch (err) {
          console.error('复制图片失败', err);
          notify('复制图片失败，请重试', 'error');
        }
      }, 'image/png');
    } catch (error) {
      console.error('复制失败:', error);
      notify('复制失败，请检查浏览器权限', 'error');
    }
  };

  // 保存当前渲染结果到本地
  const handleSaveImage = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    canvas.toBlob((blob) => {
      if (!blob) return;
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'render.png';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 'image/png');
  };
  // --------------------------------------------------------------

  return (
    <div className="render-page">

      <div className="title-bar">
        <div className="title-bar__left">
          <img className="logo" src={LogoImage} alt="RINKO" onClick={handleLogoClick} style={{ height: '32px' }}/>
        </div>
        <div className="user-controls">
          <SecondaryButton icon={Copy} onClick={handleCopyImage}>复制图片</SecondaryButton>
          <PrimaryButton icon={Download} onClick={handleSaveImage}>保存图片</PrimaryButton>
        </div>
      </div>

      <div className="render-container">
        <div className="render-window">
          {/* 3D渲染区域 */}
          <div className="render-area">
            <Canvas
              onCreated={({ gl }) => { canvasRef.current = gl.domElement; }}
              gl={{ preserveDrawingBuffer: true, alpha: true }}
              shadows camera={{ position: [0, 1, 4], fov: 45 }} style={{ background: 'transparent' }}>
              <fog attach="fog" args={[theme === 'light' ? '#F5F5F5' : '#1A1A1A', 5, 20]} />
              {showLoader ? (
                <Loader />
              ) : (
                <Suspense fallback={<Loader />}>
                  {currentModel && (uploadedModelUrl || currentModel.filePath) ? (
                    <Stage environment="city" intensity={0.6} adjustCamera={false} shadows={false} preset="rembrandt" scale={1}>
                      <Model
                        modelPath={uploadedModelUrl || currentModel.filePath || ''}
                        perMeshMaterials={perMeshMaterials}
                        activeMaterialName={activeMaterialName}
                        onOriginalMaterialsExtracted={handleOriginalMaterialsExtracted}
                      />
                    </Stage>
                  ) : (
                    <Html center>
                      <div style={{
                        color: '#fff', 
                        textAlign: 'center',
                        padding: '20px',
                        maxWidth: '300px'
                      }}>
                        <div>未选择模型或模型数据缺失</div>
                        <div style={{marginTop: '10px', fontSize: '0.9em', opacity: 0.8}}>
                          请从左侧选择模型或点击"上传模型"按钮
                        </div>
                      </div>
                    </Html>
                  )}
                  <Environment preset="city" />
                </Suspense>
              )}
              <OrbitControls 
                ref={controlsRef}
                makeDefault 
                enablePan={true} 
                enableZoom={true} 
                enableRotate={true} 
              />
            </Canvas>
          </div>
          
          <div className="button-container">
            <div 
              className="control-button"
              onClick={resetView}
              style={{ cursor: 'pointer' }}
            >
              <div className="icon-wrapper">
                <RotateCcw size={16} />
              </div>
              <span>默认视图</span>
            </div>
            <div className="control-button">
              <div className="icon-wrapper">
                <HelpCircle size={16} />
              </div>
              <span>操作说明</span>
            </div>
          </div>
        </div>
        
        {/* 属性面板 */}
        <div className="property-panel">
          <div className="panel-section">
            <div className="section-header">
              <div className="icon-wrapper">
                <Box size={16} />
              </div>
              <span>模型</span>
            </div>
            <div className="dropdown-wrapper">
              <DropDown
                options={models.map(model => ({ value: model.id, label: model.name }))}
                value={selectedModel}
                onChange={(value) => {
                  console.log('下拉框选择变化:', value);
                  setSelectedModel(value as string);
                }}
                placeholder={models.length === 0 ? '暂无模型' : ''}
              />
            </div>
            <div className="upload-button-wrapper">
              <SecondaryButton 
                icon={Upload} 
                fullWidth
                onClick={() => setUploadModalVisible(true)}
              >
                上传模型
              </SecondaryButton>
            </div>
          </div>
          
          <div className="panel-section" style={{ flex: 1 }}>
            <div className="section-header">
              <div className="icon-wrapper">
                <Paintbrush size={16} />
              </div>
              <span>材质设置</span>
            </div>
            
            <div className="editable-materials-grid">
              {originalMaterials.filter(material => material.isEditable).length > 0 ? (
                <div className="materials-grid">
                  {originalMaterials
                    .filter(material => material.isEditable)
                    .map((material, index) => {
                      // 获取当前应用的材质设置，如果有的话
                      const appliedMaterial = perMeshMaterials[material.meshName];

                      // 创建一个临时的MaterialData对象用于MaterialThumbnail
                      // 如果有应用的材质，使用应用的材质属性；否则使用原始材质属性
                      const materialData = {
                        id: `original-${material.name}`,
                        name: material.name,
                        thumbnail: '',
                        color: appliedMaterial ? appliedMaterial.color : material.color,
                        metalness: appliedMaterial ? Math.round(appliedMaterial.metalness * 100) : Math.round(material.metalness * 100),
                        roughness: appliedMaterial ? Math.round(appliedMaterial.roughness * 100) : Math.round(material.roughness * 100),
                        glass: appliedMaterial ? Math.round((1 - appliedMaterial.opacity) * 100) : Math.round((1 - material.opacity) * 100),
                        createdAt: ''
                      };

                      return (
                        <MaterialThumbnail
                          key={`${material.name}-${index}`}
                          material={materialData}
                          active={material.name === activeMaterialName}
                          onClick={() => handleMaterialActivate(material.name)}
                        />
                      );
                    })}
                </div>
              ) : (
                <div className="no-materials">
                  暂无可编辑材质
                </div>
              )}
            </div>
            
            <TabGroup 
              className="tab-switch"
              gap={4}
              defaultActiveIndex={activeTab === '会通材料' ? 0 : 1}
              onChange={(index) => setActiveTab(index === 0 ? '会通材料' : '自定义')}
            >
              <TabItem label="会通材料" className="tab-item" />
              <TabItem label="自定义" className="tab-item" />
            </TabGroup>
            
            <div className="materials-container">
              {activeTab === '会通材料' ? (
                <>
                  <div className="search-wrapper">
                    <SearchBox 
                      placeholder="搜索" 
                      value={searchQuery} 
                      onChange={(value) => setSearchQuery(value)} 
                      onSearch={() => console.log('搜索:', searchQuery)}
                    />
                  </div>
                  
                  <div className="materials-grid preset-materials">
                    {materials
                      .filter(material => material.name.includes(searchQuery))
                      .map((material) => (
                        <MaterialThumbnail
                          key={material.id}
                          material={material}
                          active={material.id === selectedPresetId}
                          size="large"
                          onClick={() => {
                            // 应用预设材质到当前激活的材质
                            setSelectedPresetId(material.id);
                             applyPresetMaterialToActive(material);
                          }}
                        />
                      ))}
                  </div>
                </>
              ) : (
                <>
                  {activeMaterialName && originalMaterials.find(m => m.name === activeMaterialName)?.isEditable ? (
                    <CustomMaterialPanel
                      defaultSettings={{
                        color: originalMaterials.find(m => m.name === activeMaterialName)?.color || '#B39B9C',
                        metalness: originalMaterials.find(m => m.name === activeMaterialName)?.metalness || 0.5,
                        roughness: originalMaterials.find(m => m.name === activeMaterialName)?.roughness || 0.5,
                        opacity: originalMaterials.find(m => m.name === activeMaterialName)?.opacity || 1
                      }}
                      onChange={(settings) => {
                        if (!activeMaterialName) return;

                        const activeMaterial = originalMaterials.find(m => m.name === activeMaterialName);
                        if (!activeMaterial || !activeMaterial.isEditable) return;

                        const newSettings = {
                          color: settings.color,
                          metalness: settings.metalness,
                          roughness: settings.roughness,
                          opacity: settings.opacity
                        };

                        // 应用到对应的mesh
                        setPerMeshMaterials(prev => ({
                          ...prev,
                          [activeMaterial.meshName]: newSettings
                        }));
                      }}
                    />
                  ) : (
                    <div className="custom-material-disabled">
                      <div className="disabled-message">
                        {activeMaterialName ?
                          '当前材质不可编辑' :
                          '请先选择一个可编辑的材质'
                        }
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
      {uploadModalVisible && (
        <UploadModelModal 
          visible={uploadModalVisible}
          onClose={() => setUploadModalVisible(false)}
          onUpload={handleModelUpload}
        />
      )}
    </div>
  );
};

export default RenderPage;